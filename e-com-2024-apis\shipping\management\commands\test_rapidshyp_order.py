"""
Django management command to test Rapidshyp order creation with real data
"""

from datetime import datetime
from decimal import Decimal
from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from orders.models import Order, OrderItem
from products.models import Product, Category
from users.models import Address
from shipping.services import ShippingService

User = get_user_model()


class Command(BaseCommand):
    help = 'Test Rapidshyp order creation with sample data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--delivery-pincode',
            type=str,
            default='400001',
            help='Delivery pincode for testing',
        )

    def handle(self, *args, **options):
        self.stdout.write('Creating test order for Rapidshyp...\n')

        try:
            # Create or get test user
            user, created = User.objects.get_or_create(
                email='<EMAIL>',
                defaults={
                    'name': 'Test Customer',
                    'phone_number': '9876543210',
                    'is_verified': True
                }
            )
            if created:
                self.stdout.write(f'Created test user: {user.email}')
            else:
                self.stdout.write(f'Using existing test user: {user.email}')

            # Create or get test address
            address, created = Address.objects.get_or_create(
                user=user,
                address_type='SHIPPING',
                defaults={
                    'street_address': '123 Test Street',
                    'apartment': 'Apt 4B',
                    'city': 'Mumbai',
                    'state': 'Maharashtra',
                    'postal_code': options['delivery_pincode'],
                    'country': 'India',
                    'order_user_phone': '9876543210',
                    'order_user_email': '<EMAIL>',
                    'is_default': True
                }
            )
            if created:
                self.stdout.write(f'Created test address: {address.city}')

            # Create or get test category and product
            category, created = Category.objects.get_or_create(
                name='Test Category',
                defaults={'description': 'Test category for Rapidshyp testing'}
            )

            product, created = Product.objects.get_or_create(
                name='Test Product',
                defaults={
                    'description': 'Test product for Rapidshyp testing',
                    'category': category,
                    'price': Decimal('1000.00'),
                    'stock': 100,
                    'is_active': True
                }
            )
            if created:
                self.stdout.write(f'Created test product: {product.name}')

            # Create test order
            order = Order.objects.create(
                user=user,
                shipping_address=address,
                billing_address=address,
                subtotal=Decimal('1000.00'),
                gst_amount=Decimal('180.00'),
                shipping_cost=Decimal('50.00'),
                total=Decimal('1230.00'),
                status='PENDING',
                rapidshyp_enabled=True
            )

            # Create order item
            OrderItem.objects.create(
                order=order,
                product=product,
                product_name=product.name,
                quantity=1,
                unit_price=product.price,
                total_price=product.price  # Required field
            )

            self.stdout.write(f'Created test order: {order.id}')

            # First check available couriers
            self.stdout.write('\nChecking available couriers...')

            shipping_service = ShippingService()

            try:
                # Get available shipping rates first
                rates_response = shipping_service.get_shipping_rates(
                    pickup_pincode='500001',  # Our pickup pincode
                    delivery_pincode=options['delivery_pincode'],
                    weight=1.0
                )

                if rates_response.get('rapidshyp_rates'):
                    self.stdout.write('Available Rapidshyp couriers:')
                    for rate in rates_response['rapidshyp_rates']:
                        self.stdout.write(f"  - {rate.get('courier_code')}: {rate.get('courier_name')} - ₹{rate.get('total_freight')}")

                    # Try to find BlueDart first, then DTDC, then any except Shree Maruti
                    preferred_couriers = ['6001', '1001', '1002', '2001', '2002']  # BlueDart, DTDC, Delhivery
                    selected_courier = None

                    for preferred_code in preferred_couriers:
                        for rate in rates_response['rapidshyp_rates']:
                            if rate.get('courier_code') == preferred_code:
                                selected_courier = rate
                                break
                        if selected_courier:
                            break

                    # If no preferred courier found, use first non-Shree Maruti courier
                    if not selected_courier:
                        for rate in rates_response['rapidshyp_rates']:
                            if rate.get('courier_code') != '14001':  # Avoid Shree Maruti
                                selected_courier = rate
                                break

                    # Fallback to first courier if all else fails
                    if not selected_courier:
                        selected_courier = rates_response['rapidshyp_rates'][0]

                    courier_code = selected_courier.get('courier_code')
                    self.stdout.write(f'\nUsing courier: {courier_code} ({selected_courier.get("courier_name")})')
                else:
                    self.stdout.write('No Rapidshyp couriers available, using default 6001')
                    courier_code = '6001'

            except Exception as e:
                self.stdout.write(f'Error getting rates: {e}')
                courier_code = '6001'  # Fallback

            # Test Rapidshyp order creation
            self.stdout.write('\nTesting Rapidshyp order creation...')

            try:
                result = shipping_service.create_rapidshyp_order(
                    order=order,
                    courier_code=courier_code,
                    delivery_pincode=options['delivery_pincode'],
                    package_weight=1.0
                )
                
                self.stdout.write(self.style.SUCCESS('✓ Rapidshyp order created successfully!'))
                self.stdout.write(f'Result: {result}')
                
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'✗ Rapidshyp order creation failed: {e}'))
                
                # Check if there are any API logs
                from shipping.models import RapidshypAPILog
                recent_logs = RapidshypAPILog.objects.filter(
                    method='wrapper'
                ).order_by('-created_at')[:3]
                
                if recent_logs:
                    self.stdout.write('\nRecent API logs:')
                    for log in recent_logs:
                        self.stdout.write(f'- {log.created_at}: {log.is_success} - {log.error_message}')
                        if log.response_data:
                            self.stdout.write(f'  Response: {log.response_data}')

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Test setup failed: {e}'))
            raise CommandError(f'Failed to create test order: {e}')
