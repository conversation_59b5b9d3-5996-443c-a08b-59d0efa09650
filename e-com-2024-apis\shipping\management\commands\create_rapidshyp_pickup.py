"""
Create pickup location in Rapidshyp
"""

import json
import requests
from django.core.management.base import BaseCommand
from django.conf import settings
from shipping.models import RapidshypConfiguration


class Command(BaseCommand):
    help = 'Create pickup location in Rapidshyp'

    def add_arguments(self, parser):
        parser.add_argument(
            '--address-name',
            type=str,
            default='Main Warehouse',
            help='Pickup address name',
        )

    def handle(self, *args, **options):
        # Get current configuration
        config = RapidshypConfiguration.objects.filter(is_active=True).first()
        if not config:
            self.stdout.write(self.style.ERROR('No Rapidshyp configuration found'))
            return

        api_key = getattr(settings, 'RAPIDSHYP_API_KEY', None)
        base_url = getattr(settings, 'RAPIDSHYP_BASE_URL', 'https://api.rapidshyp.com/rapidshyp/apis/v1')

        if not api_key:
            self.stdout.write(self.style.ERROR('RAPIDSHYP_API_KEY not configured'))
            return

        # Setup session
        session = requests.Session()
        session.headers.update({
            'Content-Type': 'application/json',
            'rapidshyp-token': api_key,
            'User-Agent': 'Triumph-Ecommerce/1.0'
        })

        # Pickup location data
        pickup_data = {
            "address_name": options['address_name'],
            "contact_name": config.contact_name,
            "contact_number": config.contact_phone,
            "email": config.contact_email,
            "address_line": config.address_line_1,
            "pincode": config.default_pickup_pincode,
            "use_alt_rto_address": False,
            "dropship_location": False  # Added missing field
        }

        self.stdout.write(f'Creating pickup location: {options["address_name"]}')
        self.stdout.write(f'Payload: {json.dumps(pickup_data, indent=2)}')

        try:
            response = session.post(f"{base_url}/create/pickup_location", json=pickup_data, timeout=30)
            
            self.stdout.write(f'Status Code: {response.status_code}')
            
            if response.content:
                try:
                    json_data = response.json()
                    self.stdout.write(f'Response: {json.dumps(json_data, indent=2)}')
                    
                    if response.status_code == 200 and json_data.get('status') == 'SUCCESS':
                        self.stdout.write(self.style.SUCCESS('✓ Pickup location created successfully!'))
                        
                        # Update configuration with the correct address name
                        config.pickup_address_name = options['address_name']
                        config.save()
                        
                        self.stdout.write(f'Updated configuration with pickup address name: {options["address_name"]}')
                        
                    else:
                        error_msg = json_data.get('message', json_data.get('remarks', 'Unknown error'))
                        self.stdout.write(self.style.ERROR(f'✗ Failed to create pickup location: {error_msg}'))
                        
                except json.JSONDecodeError:
                    self.stdout.write(self.style.ERROR(f'✗ Invalid JSON response: {response.text}'))
            else:
                self.stdout.write(self.style.ERROR('✗ Empty response'))

        except requests.exceptions.Timeout:
            self.stdout.write(self.style.WARNING('⚠ Request timed out - this might be a temporary API issue'))
            self.stdout.write('The pickup location might still have been created. Try testing with:')
            self.stdout.write(f'python manage.py test_rapidshyp_order')

        except requests.exceptions.RequestException as e:
            self.stdout.write(self.style.ERROR(f'✗ Request failed: {e}'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'✗ Unexpected error: {e}'))

        # Always show manual creation instructions
        self.stdout.write('\n' + '='*60)
        self.stdout.write(self.style.WARNING('MANUAL PICKUP LOCATION CREATION REQUIRED'))
        self.stdout.write('='*60)
        self.stdout.write('Since the API is timing out, please create the pickup location manually:')
        self.stdout.write('')
        self.stdout.write('1. Log into your Rapidshyp dashboard at: https://rapidshyp.com/')
        self.stdout.write('2. Navigate to "Pickup Locations" or "Warehouse Management"')
        self.stdout.write('3. Create a new pickup location with these details:')
        self.stdout.write(f'   - Address Name: {options["address_name"]}')
        self.stdout.write(f'   - Contact Name: {config.contact_name}')
        self.stdout.write(f'   - Contact Phone: {config.contact_phone}')
        self.stdout.write(f'   - Email: {config.contact_email}')
        self.stdout.write(f'   - Address Line 1: {config.address_line_1}')
        self.stdout.write(f'   - Address Line 2: {config.address_line_2}')
        self.stdout.write(f'   - Pincode: {config.default_pickup_pincode}')
        self.stdout.write('4. Note the EXACT name you give to the pickup location')
        self.stdout.write('5. Test with: python manage.py test_rapidshyp_order')
        self.stdout.write('')
        self.stdout.write('If the pickup location name differs from what you created,')
        self.stdout.write('update it with: python manage.py setup_rapidshyp_config --pickup-address-name "EXACT_NAME" --update')
