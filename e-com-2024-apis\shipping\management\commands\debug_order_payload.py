"""
Debug command to show the exact order payload being sent to Rapidshyp
"""

import json
from datetime import datetime
from decimal import Decimal
from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from orders.models import Order, OrderItem
from products.models import Product, Category
from users.models import Address
from shipping.services import ShippingService
from shipping.models import RapidshypConfiguration

User = get_user_model()


class Command(BaseCommand):
    help = 'Debug the exact order payload being sent to Rapidshyp'

    def add_arguments(self, parser):
        parser.add_argument(
            '--delivery-pincode',
            type=str,
            default='400001',
            help='Delivery pincode for testing',
        )

    def handle(self, *args, **options):
        try:
            # Get configuration
            config = RapidshypConfiguration.objects.filter(is_active=True).first()
            if not config:
                self.stdout.write(self.style.ERROR('No Rapidshyp configuration found'))
                return

            # Create or get test user
            user, created = User.objects.get_or_create(
                email='<EMAIL>',
                defaults={
                    'name': 'Test Customer',
                    'phone_number': '9876543210',
                    'is_verified': True
                }
            )

            # Create or get test address
            address, created = Address.objects.get_or_create(
                user=user,
                address_type='SHIPPING',
                defaults={
                    'street_address': '123 Test Street',
                    'apartment': 'Apt 4B',
                    'city': 'Mumbai',
                    'state': 'Maharashtra',
                    'postal_code': options['delivery_pincode'],
                    'country': 'India',
                    'order_user_phone': '9876543210',
                    'order_user_email': '<EMAIL>',
                    'is_default': True
                }
            )

            # Create or get test category and product
            category, created = Category.objects.get_or_create(
                name='Test Category',
                defaults={'description': 'Test category for Rapidshyp testing'}
            )

            product, created = Product.objects.get_or_create(
                name='Test Product',
                defaults={
                    'description': 'Test product for Rapidshyp testing',
                    'category': category,
                    'price': Decimal('1000.00'),
                    'stock': 100,
                    'is_active': True
                }
            )

            # Create test order
            order = Order.objects.create(
                user=user,
                shipping_address=address,
                billing_address=address,
                subtotal=Decimal('1000.00'),
                gst_amount=Decimal('180.00'),
                shipping_cost=Decimal('50.00'),
                total=Decimal('1230.00'),
                status='PENDING',
                rapidshyp_enabled=True
            )

            # Create order item
            OrderItem.objects.create(
                order=order,
                product=product,
                product_name=product.name,
                quantity=1,
                unit_price=product.price,
                total_price=product.price
            )

            self.stdout.write(f'Created test order: {order.id}')

            # Get shipping service and prepare order data
            shipping_service = ShippingService()
            
            # Prepare the order data manually to see what would be sent
            package_dimensions = {
                'packageLength': 20.0,
                'packageBreadth': 15.0,
                'packageHeight': 10.0
            }
            
            courier_code = '6001'  # BlueDart
            
            order_data = shipping_service._prepare_rapidshyp_order_data(
                order=order,
                config=config,
                courier_code=courier_code,
                delivery_pincode=options['delivery_pincode'],
                package_weight=1.0,
                package_dimensions=package_dimensions
            )

            self.stdout.write('\n' + '='*60)
            self.stdout.write('ORDER PAYLOAD THAT WOULD BE SENT TO RAPIDSHYP:')
            self.stdout.write('='*60)
            self.stdout.write(json.dumps(order_data, indent=2, default=str))
            self.stdout.write('='*60)

        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Debug failed: {e}'))
            import traceback
            traceback.print_exc()
